import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Text } from '@/components/ui/text';
import { SelectionGroup } from '@/components/selection-group';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useAppStore } from '@/lib/store';
import { ThreeXUIConfig } from '@/lib/types';
import { router, useLocalSearchParams, useNavigation } from 'expo-router';
import React, { useState, useEffect } from 'react';
import { ScrollView, StyleSheet, View, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { getThreeXUIInboundList } from '@/panels/3x-ui/utils';

// 路由规则类型定义
interface RouteRule {
  domainMatcher: 'hybrid' | 'linear';
  type: 'field';
  domain?: string[];
  ip?: string[];
  port?: string;
  sourcePort?: string;
  network?: 'tcp' | 'udp' | 'tcp,udp';
  source?: string[];
  user?: string[];
  inboundTag?: string[];
  protocol?: ('http' | 'tls' | 'quic' | 'bittorrent')[];
  attrs?: Record<string, string>;
  outboundTag?: string;
  balancerTag?: string;
}

// 预设选项
const DOMAIN_PRESETS = [
  'geosite:apple',
  'geosite:meta', 
  'geosite:google',
  'geosite:openai',
  'geosite:spotify',
  'geosite:netflix',
  'geosite:reddit',
  'geosite:speedtest'
];

const IP_PRESETS = [
  'geoip:private',
  'geoip:cn',
  'geoip:vn',
  'geoip:es',
  'geoip:ua',
  'geoip:id',
  'geoip:tr',
  'geoip:br',
  'ext:geoip_IR.dat:ir',
  'ext:geoip_RU.dat:ru',
];

const PROTOCOL_OPTIONS = [
  { id: 'http', label: 'HTTP' },
  { id: 'tls', label: 'TLS' },
  { id: 'quic', label: 'QUIC' },
  { id: 'bittorrent', label: 'BitTorrent' }
];

const NETWORK_OPTIONS = [
  { id: 'tcp', label: 'TCP' },
  { id: 'udp', label: 'UDP' },
  { id: 'tcp,udp', label: 'TCP,UDP' }
];

const DOMAIN_MATCHER_OPTIONS = [
  { id: 'hybrid', label: 'Hybrid' },
  { id: 'linear', label: 'Linear' },
];

export default function RuleConfigScreen() {
  const { configId, ruleIndex } = useLocalSearchParams<{ configId: string; ruleIndex?: string }>();
  const backgroundColor = useThemeColor({}, 'background');
  const textColor = useThemeColor({}, 'text');
  const borderColor = useThemeColor({}, 'border');

  const { getServerConfig, setServerConfig, configs } = useAppStore();

  // 表单状态
  const [domainMatcher, setDomainMatcher] = useState<'hybrid' | 'linear'>('hybrid');
  const [domainInput, setDomainInput] = useState('');
  const [selectedDomainPresets, setSelectedDomainPresets] = useState<string[]>([]);
  const [ipInput, setIpInput] = useState('');
  const [selectedIpPresets, setSelectedIpPresets] = useState<string[]>([]);
  const [port, setPort] = useState('');
  const [sourcePort, setSourcePort] = useState('');
  const [network, setNetwork] = useState<string>('');
  const [source, setSource] = useState('');
  const [user, setUser] = useState('');
  const [selectedInboundTags, setSelectedInboundTags] = useState<string[]>([]);
  const [selectedProtocols, setSelectedProtocols] = useState<string[]>([]);
  const [attrsMethod, setAttrsMethod] = useState('');
  const [outboundTag, setOutboundTag] = useState('');
  const [balancerTag, setBalancerTag] = useState('');

  const navigation = useNavigation();

  // 获取当前配置
  const currentConfig = configs.find(c => c.id === configId) as ThreeXUIConfig;
  const serverConfig = getServerConfig(configId || '');
  
  // 获取入站和出站标签选项
  const inboundOptions = (serverConfig?.inbounds || []).map((inbound: any) => ({
    id: inbound.tag || `${inbound.protocol}-${inbound.port}`,
    label: inbound.tag || `${inbound.protocol}-${inbound.port}`
  }));

  const outboundOptions = (serverConfig?.xray?.outbounds || []).map((outbound: any) => ({
    id: outbound.tag,
    label: outbound.tag
  }));

  // 如果没有入站数据，则在此页面内主动拉取一次
  useEffect(() => {
    const fetchInboundsIfNeeded = async () => {
      if (!currentConfig) return;
      const inbounds = serverConfig?.inbounds || [];
      if (inbounds.length === 0) {
        try {
          await getThreeXUIInboundList(currentConfig);
        } catch (e) {
          console.error('Fetch inbounds in rule screen failed:', e);
        }
      }
    };
    fetchInboundsIfNeeded();
    // 依赖 inbounds 长度变化来触发重试/刷新
  }, [currentConfig, serverConfig?.inbounds?.length]);

  // 编辑模式：加载现有规则数据
  useEffect(() => {
    if (ruleIndex !== undefined && serverConfig?.xray?.routing?.rules) {
      const rules = serverConfig.xray.routing.rules;
      const index = parseInt(ruleIndex);
      if (index >= 0 && index < rules.length) {
        const rule = rules[index];
        loadRuleData(rule);
      }
    }
  }, [ruleIndex, serverConfig]);

  useEffect(() => {
      if (ruleIndex !== undefined) {
        navigation.setOptions({
          title: '编辑路由规则'
        });
      } else {
        navigation.setOptions({
          headerRight: undefined,
          title: '添加路由规则'
        });
      }
    }, [navigation, ruleIndex]);

  const loadRuleData = (rule: RouteRule) => {
    setDomainMatcher(rule.domainMatcher || 'hybrid');
    
    // 处理域名数据
    if (rule.domain) {
      const domains = rule.domain.filter(d => !DOMAIN_PRESETS.includes(d));
      const presets = rule.domain.filter(d => DOMAIN_PRESETS.includes(d));
      setDomainInput(domains.join(', '));
      setSelectedDomainPresets(presets);
    }

    // 处理IP数据
    if (rule.ip) {
      const ips = rule.ip.filter(ip => !IP_PRESETS.includes(ip));
      const presets = rule.ip.filter(ip => IP_PRESETS.includes(ip));
      setIpInput(ips.join(', '));
      setSelectedIpPresets(presets);
    }

    setPort(rule.port || '');
    setSourcePort(rule.sourcePort || '');
    setNetwork(rule.network || '');
    setSource(rule.source?.join(', ') || '');
    setUser(rule.user?.join(', ') || '');
    setSelectedInboundTags(rule.inboundTag || []);
    setSelectedProtocols(rule.protocol || []);
    setAttrsMethod(rule.attrs?.[':method'] || '');
    setOutboundTag(rule.outboundTag || '');
    setBalancerTag(rule.balancerTag || '');
  };

  // 保存规则
  const handleSave = () => {
    if (!currentConfig) {
      Alert.alert('错误', '未找到配置信息');
      return;
    }

    // 构建域名数组
    const domainArray: string[] = [];
    if (domainInput.trim()) {
      const inputDomains = domainInput.split(',').map(d => d.trim()).filter(d => d);
      domainArray.push(...inputDomains);
    }
    domainArray.push(...selectedDomainPresets);

    // 构建IP数组
    const ipArray: string[] = [];
    if (ipInput.trim()) {
      const inputIps = ipInput.split(',').map(ip => ip.trim()).filter(ip => ip);
      ipArray.push(...inputIps);
    }
    ipArray.push(...selectedIpPresets);

    // 构建规则对象
    const rule: RouteRule = {
      domainMatcher,
      type: 'field'
    };

    // 只添加非空字段
    if (domainArray.length > 0) rule.domain = [...new Set(domainArray)]; // 去重
    if (ipArray.length > 0) rule.ip = [...new Set(ipArray)]; // 去重
    if (port.trim()) rule.port = port.trim();
    if (sourcePort.trim()) rule.sourcePort = sourcePort.trim();
    if (network) rule.network = network as any;
    if (source.trim()) {
      rule.source = source.split(',').map(s => s.trim()).filter(s => s);
    }
    if (user.trim()) {
      rule.user = user.split(',').map(u => u.trim()).filter(u => u);
    }
    if (selectedInboundTags.length > 0) rule.inboundTag = selectedInboundTags;
    if (selectedProtocols.length > 0) rule.protocol = selectedProtocols as any;
    if (attrsMethod.trim()) {
      rule.attrs = { ':method': attrsMethod.trim() };
    }
    if (outboundTag.trim()) rule.outboundTag = outboundTag.trim();
    if (balancerTag.trim()) rule.balancerTag = balancerTag.trim();

    // 更新服务器配置
    const updatedServerConfig = { ...serverConfig };
    if (!updatedServerConfig.xray) updatedServerConfig.xray = {};
    if (!updatedServerConfig.xray.routing) updatedServerConfig.xray.routing = {};
    if (!updatedServerConfig.xray.routing.rules) updatedServerConfig.xray.routing.rules = [];

    const rules = [...updatedServerConfig.xray.routing.rules];
    
    if (ruleIndex !== undefined) {
      // 编辑模式
      const index = parseInt(ruleIndex);
      if (index >= 0 && index < rules.length) {
        rules[index] = rule;
      }
    } else {
      // 添加模式
      rules.push(rule);
    }

    updatedServerConfig.xray.routing.rules = rules;
    setServerConfig(configId || '', updatedServerConfig);

    Alert.alert('成功', '规则已保存', [
      { text: '确定', onPress: () => router.back() }
    ]);
  };

  return (
    <>
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <ScrollView style={styles.scrollView} showsHorizontalScrollIndicator={false} contentContainerStyle={styles.content}>
        {/* 域名匹配器 */}
        <View style={styles.section}>
          <SelectionGroup
            label="域名匹配器"
            options={DOMAIN_MATCHER_OPTIONS}
            value={domainMatcher}
            onChange={(value) => setDomainMatcher(value as 'hybrid' | 'linear')}
            multiple={false}
          />
        </View>

        {/* 域名规则 */}
        <View style={styles.section}>
          <Label style={[styles.label, { color: textColor }]}>域名 (Domain)</Label>
          <Input
            style={[styles.input, { borderColor }]}
            value={domainInput}
            onChangeText={setDomainInput}
            placeholder="输入域名，用逗号分隔"
          />
          <SelectionGroup
            options={DOMAIN_PRESETS.map(preset => ({ id: preset, label: preset }))}
            value={selectedDomainPresets}
            onChange={(value) => setSelectedDomainPresets(value as string[])}
            multiple
          />
        </View>

        {/* IP规则 */}
        <View style={styles.section}>
          <Label style={[styles.label, { color: textColor }]}>IP地址 (IP)</Label>
          <Input
            style={[styles.input, { borderColor }]}
            value={ipInput}
            onChangeText={setIpInput}
            placeholder="输入IP地址或CIDR，用逗号分隔"
          />
          <SelectionGroup
            options={IP_PRESETS.map(preset => ({ id: preset, label: preset }))}
            value={selectedIpPresets}
            onChange={(value) => setSelectedIpPresets(value as string[])}
            multiple
          />
        </View>

        {/* 端口 */}
        <View style={styles.section}>
          <Label style={[styles.label, { color: textColor }]}>端口 (Port)</Label>
          <Input
            style={[styles.input, { borderColor }]}
            value={port}
            onChangeText={setPort}
            placeholder="例如: 53,443,1000-2000"
          />
        </View>

        {/* 源端口 */}
        <View style={styles.section}>
          <Label style={[styles.label, { color: textColor }]}>源端口 (Source Port)</Label>
          <Input
            style={[styles.input, { borderColor }]}
            value={sourcePort}
            onChangeText={setSourcePort}
            placeholder="例如: 53,443,1000-2000"
          />
        </View>

        {/* 网络类型 */}
        <View style={styles.section}>
          <SelectionGroup
            label="网络类型 (Network)"
            options={NETWORK_OPTIONS}
            value={network}
            onChange={(value) => setNetwork(value as string)}
          />
        </View>

        {/* 源地址 */}
        <View style={styles.section}>
          <Label style={[styles.label, { color: textColor }]}>源地址 (Source)</Label>
          <Input
            style={[styles.input, { borderColor }]}
            value={source}
            onChangeText={setSource}
            placeholder="输入源IP地址，用逗号分隔"
          />
        </View>

        {/* 用户 */}
        <View style={styles.section}>
          <Label style={[styles.label, { color: textColor }]}>用户 (User)</Label>
          <Input
            style={[styles.input, { borderColor }]}
            value={user}
            onChangeText={setUser}
            placeholder="输入用户邮箱，用逗号分隔"
          />
        </View>

        {/* 入站标签 */}
        <View style={styles.section}>
          <SelectionGroup
            label="入站标签 (Inbound Tag)"
            options={inboundOptions}
            value={selectedInboundTags}
            onChange={(value) => setSelectedInboundTags(value as string[])}
            multiple
          />
        </View>

        {/* 协议 */}
        <View style={styles.section}>
          <SelectionGroup
            label="协议 (Protocol)"
            options={PROTOCOL_OPTIONS}
            value={selectedProtocols}
            onChange={(value) => setSelectedProtocols(value as string[])}
          />
        </View>

        {/* 属性 */}
        <View style={styles.section}>
          <Label style={[styles.label, { color: textColor }]}>HTTP方法 (Attrs :method)</Label>
          <Input
            style={[styles.input, { borderColor }]}
            value={attrsMethod}
            onChangeText={setAttrsMethod}
            placeholder="例如: GET, POST"
          />
        </View>

        {/* 出站标签 */}
        <View style={styles.section}>
          <SelectionGroup
            label="出站标签 (Outbound Tag)"
            options={outboundOptions}
            value={outboundTag}
            onChange={(value) => setOutboundTag(value as string)}
            multiple={false}
          />
        </View>

        {/* 负载均衡标签 */}
        <View style={styles.section}>
          <Label style={[styles.label, { color: textColor }]}>负载均衡标签 (Balancer Tag)</Label>
          <Input
            style={[styles.input, { borderColor }]}
            value={balancerTag}
            onChangeText={setBalancerTag}
            placeholder="输入负载均衡标签"
          />
        </View>

      </ScrollView>

      {/* 保存按钮 */}
        <View style={[styles.footer, { borderTopColor: borderColor, backgroundColor }]}>
        <Button onPress={handleSave} style={styles.saveButton}>
          <Text style={styles.saveButtonText}>保存规则</Text>
        </Button>
      </View>
    </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
    paddingBottom:36
  },
  section: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 16,
    marginBottom: 8,
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
    borderTopWidth: 1,
  },
  saveButton: {
    width: '100%',
  },
  saveButtonText: {
    fontWeight: '600',
  },
});
